import time
import re
from playwright.sync_api import Page
from init import show_waiting_dots, try_click_with_loading_check
from paychoice import extract_all_amounts, choose_payment_method
from payment import input_payment_password

def purchase(page: Page):
    """购买流程"""
    # 确保进入首页
    page.goto("https://h5.chengzhuo.site", timeout=30000)
    # 等待进行中按钮
    in_progress_loc = page.locator("text=进行中")
    try:
        in_progress_loc.first.wait_for(state="visible", timeout=7000)
        in_progress_loc.first.scroll_into_view_if_needed()
    except Exception:
        print("没有进行中的活动")
        return


    # 执行购买步骤（每个 tick 仅尝试一次，不进行阻塞等待）
    def try_click(loc):
        try:
            if loc.count() > 0 and loc.first.is_visible():
                loc.first.click()
                return True
        except Exception:
            return False
        return False

    steps = [
        ("进行中", lambda: try_click_with_loading_check(page, in_progress_loc)),
        ("立即参与", lambda: try_click_with_loading_check(page, page.get_by_text("立即参与"))),
        ("一键购买", lambda: try_click(page.get_by_text("一键购买"))),
        ("确认", lambda: try_click(page.locator("uni-view", has_text=re.compile(r"^确认$")))),
        ("提交订单", lambda: try_click_with_loading_check(page.locator("uni-view.btn", has_text="提交订单"))),
    ]

    idx = 0
    failed = {"desc": None, "error": None}
    aborted_due_to_insufficient = False

    def safe_click(locator):
        try:
            locator.click()
            return True
        except Exception:
            return False

    def handle_insufficient():
        nonlocal aborted_due_to_insufficient
        try:
            zero_tip = page.get_by_text("最多可购买此商品：0件")
            nan_tip = page.get_by_text("最多可购买此商品：NaN件")
            time.sleep(2)
            if zero_tip.count() > 0 or nan_tip.count() > 0:
                # 依次确认弹窗
                safe_click(page.locator("uni-view").filter(has_text=re.compile(r"^确认$")))
                safe_click(page.get_by_text("确认"))
                # 返回首页
                page.goto("https://h5.chengzhuo.site", timeout=30000)
                aborted_due_to_insufficient = True
                return True
        except Exception:
            pass
        return False

    def check_purchase():
        nonlocal idx
        nonlocal aborted_due_to_insufficient
        if failed["desc"] is not None:
            return False
        if idx >= len(steps):
            return True
        desc, try_step = steps[idx]
        try:
            if try_step():
                # 一键购买后检查是否金额不足；若不足则标记并由 abort_func 让动画以 ✗ 结束
                if desc == "一键购买" and handle_insufficient():
                    return False
                idx += 1
        except Exception as e:
            failed["desc"] = desc
            failed["error"] = e
            return False
        return False

    if not show_waiting_dots(
        "执行中：参拍中",
        check_purchase,
        45,
        abort_func=lambda: page.is_closed() or aborted_due_to_insufficient,
    ):
        if aborted_due_to_insufficient:
            print("余额不足或参拍失败")
            return
        if failed["desc"] is not None:
            print(f"执行失败：{failed['desc']} - {failed['error']}")
        else:
            print("参拍超时未完成")
        return

    # 如果因为金额不足或参拍失败而提前终止，则直接返回
    if aborted_due_to_insufficient:
        return

    # 支付处理
    time.sleep(1)
    amounts = extract_all_amounts(page)
    method = choose_payment_method(page, amounts)
    if method is None:
        return
    
    # 确认付款
    time.sleep(1)
    page.get_by_text("确定付款").click()
    
    # 输入密码
    time.sleep(1)
    if page.get_by_text("请输入支付密码").count() > 0:
        from payment import input_payment_password
        if input_payment_password(page):
            time.sleep(3)
            page.goto("https://h5.chengzhuo.site", timeout=30000)
            print("支付完成✓")
