"""简单的命令行菜单，用于选择要执行的操作。"""
from typing import Callable


def prompt_menu(options: dict) -> str:
    """显示菜单并返回选项的 key。options 为 key->描述 的映射。"""
    keys = list(options.keys())
    print("="*20)
    print("请选择要执行的操作：")
    for i, k in enumerate(keys, start=1):
        print(f"{i}. {options[k]}")
    print("="*20)

    while True:
        choice = input("输入序号: ")
        try:
            idx = int(choice) - 1
            if 0 <= idx < len(keys):
                return keys[idx]
        except:
            pass
        print("无效选择，请重试。")
