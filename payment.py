import time
from playwright.sync_api import Page
from config import DEFAULT_PAYMENT_PASSWORD

def input_payment_password(page: Page):
    """自动输入支付密码"""
    password = DEFAULT_PAYMENT_PASSWORD
    print(f"正在输入支付密码...")

    for digit in password:
        try:
            page.get_by_text(digit, exact=True).click()
            time.sleep(0.3)
        except:
            print(f"密码输入失败，无法找到数字: {digit}")
            return False

    print("密码输入成功")
    return True


def complete_payment(page: Page):
    """完成支付流程"""
    try:
        if not input_payment_password(page):
            return False

        time.sleep(2)
        page.get_by_text("返回首页").click()
        return True

    except:
        print("支付失败")
        return False
