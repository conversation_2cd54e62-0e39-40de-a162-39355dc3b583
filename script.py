from init import run_script_with_browser, safe_wait_for_load_state
from agreement import wait_for_page_ready
from agreement import handle_agreements
from login import login
from purchase import purchase
from settlement import settlement
from Menu import prompt_menu
from playwright.sync_api import Page, <PERSON><PERSON>er

def run_main_script(page: Page, browser: Browser) -> None:
    """主脚本逻辑。"""
    # 1. 导航到页面
    print("正在导航到页面...")
    page.goto("https://h5.chengzhuo.site/")
    # 使用“不同意并退出”出现来判断页面是否加载完成
    if not wait_for_page_ready(page, timeout=8.0):
        print("页面加载失败")
        try:
            browser.close()
        except Exception:
            pass
        return
    print("页面加载完成")

    # 2. 处理页面同意操作
    if not handle_agreements(page):
        print("同意操作失败，脚本终止")
        try:
            browser.close()
        except Exception:
            pass
        return

    # 3. 执行登录流程
    if not login(page):
        print("登录失败，脚本终止")
        try:
            browser.close()
        except Exception:
            pass
        return

    # 登录后等待页面稳定
    safe_wait_for_load_state(page)

    # 菜单循环：购买、结算、退出。每次操作完成后返回菜单（无论成功或失败）
    options = {
        'purchase': '参拍',
        'settlement': '结算',
        'exit': '退出'
    }

    while True:
        choice = prompt_menu(options)
        if choice == 'purchase':
            try:
                purchase(page)
            except Exception as e:
                print(f"购买流程出错: {e}")

        elif choice == 'settlement':
            try:
                settlement(page)
            except Exception as e:
                print(f"结算流程出错: {e}")

        elif choice == 'exit':
            try:
                browser.close()
            except Exception:
                pass
            print("脚本已终止。")
            return

        # 每次选项执行完后继续显示菜单

# 使用公共函数来运行脚本
if __name__ == "__main__":
    run_script_with_browser(run_main_script)