import time
from playwright.sync_api import Page
from init import show_waiting_dots
try:
    from config import DEFAULT_USERNAME, DEFAULT_PASSWORD
except Exception:
    DEFAULT_USERNAME = ""
    DEFAULT_PASSWORD = ""

def login(page: Page) -> bool:
    """登录流程"""
    try:
        # 进入登录页面
        page.locator("div").filter(has_text="我的").nth(3).click()
        page.get_by_text("立即登录").click()
        
        # 等待页面稳定
        try:
            page.wait_for_load_state("networkidle", timeout=3000)
        except:
            pass

        # 填写账号密码
        if DEFAULT_USERNAME and DEFAULT_PASSWORD:
            page.locator("input[type=\"text\"]").fill(DEFAULT_USERNAME)
            page.locator("input[type=\"password\"]").fill(DEFAULT_PASSWORD)
        else:
            username = input("请输入账号: ")
            password = input("请输入密码: ")
            page.locator("input[type=\"text\"]").fill(username)
            page.locator("input[type=\"password\"]").fill(password)

        # 点击登录并等待成功，处理页面卡顿情况
        login_btn = page.locator("uni-button").filter(has_text="登录")
        try:
            login_btn.first.wait_for(state="visible", timeout=10000)
        except Exception:
            return False
        
        # 持续检查登录状态直到成功
        def click_login_until_success():
            try:
                # 检查是否已跳转到首页（登录成功）
                if "chengzhuo.site" in page.url and "user-login" not in page.url:
                    return True
                
                # 尝试点击登录按钮
                if login_btn.first.is_visible():
                    try:
                        login_btn.first.click()
                    except Exception:
                        pass  # 被遮挡也继续检查
                    time.sleep(1)
                
                return False  # 继续等待
            except Exception:
                # 异常时检查是否已在首页
                try:
                    return "chengzhuo.site" in page.url and "user-login" not in page.url
                except Exception:
                    return False
        
        return show_waiting_dots("执行中：登录中", click_login_until_success, 30)

    except Exception as e:
        print(f"登录失败: {e}")
        return False