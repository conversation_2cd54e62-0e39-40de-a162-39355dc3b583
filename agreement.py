from playwright.sync_api import Page
from init import show_waiting_dots, safe_wait_for_load_state


def handle_agreements(page: Page):
    """处理页面的同意操作"""
    try:
        # 点击"全部同意"
        if show_waiting_dots("执行中：全部同意", lambda: page.locator("text=全部同意").is_visible(), 3):
            try:
                page.locator("text=全部同意").click()
            except:
                page.locator("uni-label").filter(has_text="全部同意").locator("div").nth(1).click()

        # 点击"同意"
        if show_waiting_dots("执行中：同意", lambda: page.get_by_text("同意", exact=True).is_visible(), 3):
            page.get_by_text("同意", exact=True).click()

        return True

    except Exception as e:
        print(f"同意操作过程中出现错误: {e}")
        return False


def wait_for_page_ready(page: Page, timeout: float = 8.0) -> bool:
    """综合判断页面加载完成：检查关键元素和页面状态。
    返回 True 表示已加载完成；False 表示超时或窗口已关闭。
    """
    def check_page_ready():
        try:
            # 先等待基本DOM加载
            safe_wait_for_load_state(page, 1000)
            
            # 检查关键元素是否出现（多个条件之一满足即可）
            return (page.get_by_text("不同意并退出").is_visible() or 
                    page.get_by_text("全部同意").is_visible() or
                    page.locator("body").is_visible())
        except Exception:
            return False
    
    return show_waiting_dots(
        "执行中：页面加载中",
        check_page_ready,
        timeout,
        abort_func=lambda: page.is_closed(),
    )
