import re
from playwright.sync_api import Page

def extract_all_amounts(page: Page):
    """提取支付金额、余额和积分。仅返回数据，不打印冗余日志。"""
    result = {}

    # 支付金额
    try:
        money_elements = page.get_by_text("￥", exact=False)
        for i in range(money_elements.count()):
            if money_elements.nth(i).inner_text().strip() == "￥":
                parent_text = money_elements.nth(i).locator("..").inner_text()
                m = re.search(r"(\d+(?:\.\d+)?)", parent_text)
                if m:
                    result["payment_amount"] = f"￥ {m.group(1)}"
                    break
    except Exception:
        pass

    # 余额
    try:
        t = page.get_by_text("余额支付（当前余额：", exact=False)
        if t.count() > 0:
            m = re.search(r"当前余额：(\d+(?:\.\d+)?)", t.first.inner_text())
            if m:
                result["balance"] = m.group(1)
    except Exception:
        pass

    # 积分
    try:
        t = page.get_by_text("品牌积分支付（当前积分：", exact=False)
        if t.count() > 0:
            m = re.search(r"当前积分：(\d+(?:\.\d+)?)", t.first.inner_text())
            if m:
                result["points"] = m.group(1)
    except Exception:
        pass

    return result

def extract_payment_amount(page: Page):
    """保持原有接口兼容性。找到则返回金额，并简要打印一行摘要。"""
    a = extract_all_amounts(page)
    if "payment_amount" in a:
        print(
            f"识别到支付金额: {a['payment_amount']}"
            + (f"，余额: {a['balance']}" if 'balance' in a else "")
            + (f"，积分: {a['points']}" if 'points' in a else "")
        )
        return a["payment_amount"]
    print("未能提取到金额")
    return None

def choose_payment_method(page: Page, amounts: dict):
    """根据金额/余额/积分选择支付方式并点击。返回 'points'/'balance'/None。"""
    try:
        amt = amounts.get("payment_amount")
        if not amt:
            print("未识别到支付金额")
            return None
        m = re.search(r"(\d+(?:\.\d+)?)", str(amt))
        if not m:
            print("无法解析支付金额数值")
            return None
        need = float(m.group(1))
        points = float(amounts.get("points", "0"))
        balance = float(amounts.get("balance", "0"))

        print(f"支付: {amt}，积分: {points}，余额: {balance}")

        if points >= need:
            print("使用积分支付")
            page.locator("uni-view").filter(has_text="品牌积分支付（当前积分：").nth(1).click()
            return "points"
        if balance >= need:
            print("使用余额支付")
            page.locator("uni-view").filter(has_text="余额支付（当前余额：").nth(1).click()
            return "balance"

        print("余额不足")
        return None
    except Exception:
        print("选择支付方式失败")
        return None
