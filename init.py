import time
import sys
from playwright.sync_api import Playwright, sync_playwright, <PERSON>, <PERSON><PERSON><PERSON>

def setup_browser(playwright: Playwright) -> (<PERSON><PERSON><PERSON>, <PERSON>):
    """初始化浏览器并返回浏览器实例和新的页面。"""
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context()
    page = context.new_page()
    return browser, page

def show_waiting_dots(message, check_func, max_wait=30, abort_func=None):
    """简洁的等待动画：条件满足输出 ✓，超时/中止输出 ✗。"""
    spinner = ['/', '—', '\\', '|']
    start = time.time()
    i = 0
    while True:
        sys.stdout.write(f"\r{message} {spinner[i % 4]}")
        sys.stdout.flush()
        i += 1

        # 中止条件（如窗口关闭）
        if abort_func:
            try:
                if abort_func():
                    sys.stdout.write(f"\r{message} ✗\n")
                    sys.stdout.flush()
                    return False
            except Exception:
                sys.stdout.write(f"\r{message} ✗\n")
                sys.stdout.flush()
                return False

        # 完成条件
        try:
            if check_func():
                sys.stdout.write(f"\r{message} ✓\n")
                sys.stdout.flush()
                return True
        except Exception:
            pass

        time.sleep(0.2)
        if time.time() - start >= max_wait:
            sys.stdout.write(f"\r{message} ✗\n")
            sys.stdout.flush()
            return False

def wait_for_browser_close(page: Page):
    """静默等待用户关闭浏览器窗口。"""
    try:
        while True:
            page.title()
            time.sleep(0.5)
    except Exception:
        pass

def safe_wait_for_load_state(page: Page, timeout: int = 5000):
    """安全的页面加载等待，避免H5页面networkidle超时问题"""
    try:
        page.wait_for_load_state('domcontentloaded', timeout=timeout)
    except Exception:
        pass

def refresh_page(page: Page) -> bool:
    try:
        page.reload()
        # 刷新后等待页面稳定
        safe_wait_for_load_state(page)
        return True
    except Exception as e:
        print(f"页面刷新失败: {e}")
        return False

def check_page_loading_and_refresh(page: Page, timeout: int = 5) -> bool:
    """检查页面是否卡在加载状态，超时则刷新页面
    
    Args:
        page: Playwright页面对象
        timeout: 超时时间（秒）
    
    Returns:
        bool: 页面是否准备就绪
    """
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            # 检查页面是否在加载状态
            loading_state = page.evaluate("document.readyState")
            if loading_state == "complete":
                return True
            time.sleep(0.5)
        except Exception:
            return True
    
    # 超过指定时间仍在加载，尝试刷新页面
    print("页面加载超时，尝试刷新...")
    return refresh_page(page)


def try_click_with_loading_check(page: Page, locator) -> bool:
    """点击元素并检查页面加载状态
    
    Args:
        page: Playwright页面对象
        locator: 要点击的元素定位器
    
    Returns:
        bool: 点击并加载检查是否成功
    """
    try:
        if locator.count() > 0 and locator.first.is_visible():
            locator.first.click()
            return check_page_loading_and_refresh(page)
    except Exception:
        pass
    return False


def run_script_with_browser(main_func):
    """一个处理Playwright上下文和浏览器生命周期的通用函数。"""
    with sync_playwright() as playwright:
        browser, page = setup_browser(playwright)
        try:
            main_func(page, browser)
        except Exception as e:
            print(f"脚本执行失败: {e}")
        finally:
            if browser:
                browser.close()