from playwright.sync_api import Page
import re
import time
from init import show_waiting_dots

def prepare_settlement(page: Page):
    """导航到我的-待收货-品牌推广订单。"""
    page.locator("div").filter(has_text="我的").nth(3).click()
    page.locator("uni-view").filter(has_text=re.compile(r"^待收货$")).click()
    page.get_by_text("品牌推广订单").click()
    # 页面短暂稳定
    time.sleep(1.5)
    return True


def _get_sell_change_locator(page: Page):
    """返回可点击的第一个卖了换钱的 Locator，找不到返回 None。"""
    try:
        loc = page.locator("uni-scroll-view").get_by_text("卖了换钱")
        return loc.first if loc.count() > 0 else None
    except Exception:
        return None


def _click_sell_change_if_present(page: Page) -> bool:
    """尝试点击找到的入口，成功返回 True。"""
    loc = _get_sell_change_locator(page)
    if not loc:
        return False
    try:
        loc.click()
        return True
    except Exception:
        return False


def settlement(page: Page) -> bool:
    """执行结算流程并返回是否成功。整个过程仅使用一次“执行中：结算中”动画。"""
    try:
        prepare_settlement(page)

        # 单次动画覆盖：寻找入口 -> 执行发布与确认
        loc_holder = {"loc": None}

        def do_all():
            # 先寻找入口（若未找到，返回 False 继续转动）
            if loc_holder["loc"] is None:
                loc_holder["loc"] = _get_sell_change_locator(page)
                if loc_holder["loc"] is None:
                    return False
            # 找到后执行发布+确认
            try:
                loc_holder["loc"].click()
                page.locator("uni-view").filter(has_text=re.compile(r"^立即发布$")).click()
                page.locator("uni-text").filter(has_text="确认").click()
                page.get_by_role("img").first.click()
                page.go_back()
                return True
            except Exception:
                return False

        if not show_waiting_dots("执行中：结算中", do_all, 10, abort_func=lambda: page.is_closed()):
            # 若在时限内仍未找到入口，提示后返回上一页
            if loc_holder["loc"] is None:
                print("可能已全部卖出")
                page.go_back()
            return False

        return True
    except Exception as e:
        print(f"结算流程出错: {e}")
        return False
